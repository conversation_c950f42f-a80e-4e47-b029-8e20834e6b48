/**
 * PPT演示模式专用的轻量化模型管理器
 * 直接使用ModelLoaderManager的已缓存模型，避免重复加载
 */

import * as THREE from 'three';
import { SceneManager } from '../../lib/SceneManager';
import { ModelLoaderManager } from '../../lib/load/ModelLoaderManager';
import { buildingData } from '@/data/buildingData';

// ModelLoaderManager的类型定义（用于访问私有属性）
interface ModelLoaderManagerWithPrivates {
  loadedModels: Map<string, THREE.Group | THREE.Scene>;
  getCurrentModels(): (THREE.Scene | THREE.Group)[];
  showBuilding(modelPath: string): Promise<void>;
  showFloor(floorId: string): Promise<void>;
  removeCurrentModel(): Promise<void>;
}

export class PPTModelLoader {
  private static instance: PPTModelLoader | null = null;
  private scene: THREE.Scene | null = null;
  private modelLoaderManager: ModelLoaderManager | null = null;
  private currentModel: THREE.Object3D | null = null;

  constructor() {
    if (PPTModelLoader.instance) {
      return PPTModelLoader.instance;
    }

    console.log('[PPT模型管理器] 初始化轻量化模型管理器（复用已缓存模型）');

    // 获取场景和模型加载器实例
    const sceneManager = SceneManager.getInstance();
    this.scene = sceneManager?.scene || null;
    this.modelLoaderManager = ModelLoaderManager.getInstance();

    PPTModelLoader.instance = this;
  }

  static getInstance(): PPTModelLoader {
    if (!PPTModelLoader.instance) {
      PPTModelLoader.instance = new PPTModelLoader();
    }
    return PPTModelLoader.instance;
  }

  /**
   * 从ModelLoaderManager获取已缓存的模型
   */
  private async getModelFromCache(modelPath: string): Promise<THREE.Object3D | null> {
    console.log(`[PPT模型获取] 从ModelLoaderManager获取已缓存模型: ${modelPath}`);

    if (!this.modelLoaderManager) {
      console.warn('[PPT模型获取] ModelLoaderManager实例为空');
      return null;
    }

    // 访问ModelLoaderManager的loadedModels缓存
    const loadedModels = (this.modelLoaderManager as unknown as ModelLoaderManagerWithPrivates).loadedModels;

    if (loadedModels.has(modelPath)) {
      console.log(`[PPT模型获取] 从缓存获取成功: ${modelPath}`);
      const cachedModel = loadedModels.get(modelPath);
      if (cachedModel) {
        // 克隆模型以避免影响原始缓存
        const clonedModel = cachedModel.clone();
        this.processModelForPPT(clonedModel);
        return clonedModel;
      }
    }

    // 尝试从当前场景中获取
    const currentModels = (this.modelLoaderManager as unknown as ModelLoaderManagerWithPrivates).getCurrentModels();
    for (const model of currentModels) {
      if (
        model.userData.modelPath === modelPath ||
        (model.userData.type === 'exterior' && modelPath.includes('models')) ||
        (model.userData.type === 'floor' && modelPath.includes('.glb'))
      ) {
        console.log(`[PPT模型获取] 从当前场景获取: ${modelPath}`);
        const clonedModel = model.clone();
        this.processModelForPPT(clonedModel);
        return clonedModel;
      }
    }

    // 回退机制：如果缓存中没有模型，尝试动态加载
    console.warn(`[PPT模型获取] 缓存中未找到模型 ${modelPath}，尝试动态加载...`);
    try {
      // 判断是外景模型还是楼层模型
      const { buildingData } = await import('@/data/buildingData');

      if (modelPath === buildingData.modelPath) {
        // 外景模型：使用loadExteriorModelOnly
        console.log(`[PPT模型获取] 动态加载外景模型: ${modelPath}`);
        await this.modelLoaderManager.loadExteriorModelOnly();

        // 加载完成后再次尝试从缓存获取
        if (loadedModels.has(modelPath)) {
          const cachedModel = loadedModels.get(modelPath);
          if (cachedModel) {
            const clonedModel = cachedModel.clone();
            this.processModelForPPT(clonedModel);
            return clonedModel;
          }
        }

        // 如果还是没有，从当前场景获取
        const updatedModels = (this.modelLoaderManager as unknown as ModelLoaderManagerWithPrivates).getCurrentModels();
        for (const model of updatedModels) {
          if (model.userData.type === 'exterior' || model.userData.modelPath === modelPath) {
            console.log(`[PPT模型获取] 从动态加载后的场景获取外景模型`);
            const clonedModel = model.clone();
            this.processModelForPPT(clonedModel);
            return clonedModel;
          }
        }
      } else {
        // 楼层模型：查找对应楼层并加载
        const floorData = buildingData.floors.find((floor) => floor.modelPath === modelPath);
        if (floorData) {
          console.log(`[PPT模型获取] 动态加载楼层模型: ${modelPath} (楼层ID: ${floorData.id})`);
          await (this.modelLoaderManager as unknown as ModelLoaderManagerWithPrivates).showFloor(floorData.id);

          // 加载完成后再次尝试获取
          if (loadedModels.has(modelPath)) {
            const cachedModel = loadedModels.get(modelPath);
            if (cachedModel) {
              const clonedModel = cachedModel.clone();
              this.processModelForPPT(clonedModel);
              return clonedModel;
            }
          }

          // 从当前场景获取
          const updatedModels = (this.modelLoaderManager as unknown as ModelLoaderManagerWithPrivates).getCurrentModels();
          for (const model of updatedModels) {
            if (model.userData.type === 'floor' && model.userData.floorId === floorData.id) {
              console.log(`[PPT模型获取] 从动态加载后的场景获取楼层模型`);
              const clonedModel = model.clone();
              this.processModelForPPT(clonedModel);
              return clonedModel;
            }
          }
        }
      }

      console.error(`[PPT模型获取] 动态加载失败，仍然无法获取模型: ${modelPath}`);
      return null;
    } catch (error) {
      console.error(`[PPT模型获取] 动态加载模型失败:`, error);
      return null;
    }
  }

  /**
   * 为PPT模式处理模型 - 极简化处理（仅展示用）
   */
  private processModelForPPT(model: THREE.Object3D): void {
    // PPT模式下只做最基本的处理，不进行任何复杂操作
    // 预加载的模型已经经过了完整的处理，这里只需要确保可见性
    model.visible = true;

    // 清理用户数据，避免与主场景冲突
    model.userData = {
      pptMode: true,
      originalPath: model.userData.modelPath || model.userData.path,
    };
  }

  /**
   * 根据模型路径直接切换模型（PPT专用）
   */
  async switchToModelPath(modelPath: string): Promise<void> {
    console.log(`[PPT模型切换] 直接切换到模型: ${modelPath}`);

    try {
      // 移除当前模型并等待清理完成
      await this.removeCurrentModel();

      // 从缓存获取模型
      const model = await this.getModelFromCache(modelPath);

      if (!model) {
        // 如果动态加载也失败了，抛出错误
        console.error(`[PPT模型切换] 模型 ${modelPath} 加载失败，包括动态加载尝试`);
        throw new Error(`PPT模式下模型 ${modelPath} 加载失败，请检查模型路径和网络连接`);
      } else {
        // 设置模型位置和标记
        const { buildingData } = await import('@/data/buildingData');
        if (modelPath === buildingData.modelPath) {
          model.position.set(0, -2.7, 0); // 外景模型位置调整
          model.userData.type = 'exterior';
          model.userData.pptMode = true; // 标记为PPT模式模型
          this.setExteriorLighting();
        } else {
          model.position.set(0, 0, 0);
          model.userData.type = 'floor';
          model.userData.pptMode = true; // 标记为PPT模式模型
          this.setInteriorLighting();
        }
        model.userData.modelPath = modelPath;

        // 添加到场景
        if (this.scene) {
          this.scene.add(model);
          this.currentModel = model;
        }
      }

      console.log(`[PPT模型切换] 模型 ${modelPath} 切换完成`);
    } catch (error) {
      console.error(`[PPT模型切换] 模型 ${modelPath} 切换失败:`, error);
      throw error;
    }
  }

  /**
   * 切换到外景模式（PPT专用）
   */
  async switchToExterior(): Promise<void> {
    console.log('[PPT模型切换] 切换到外景模式');
    const { buildingData } = await import('@/data/buildingData');
    await this.switchToModelPath(buildingData.modelPath);
  }

  /**
   * 切换到楼层模式（PPT专用）
   */
  async switchToFloor(floorId: string): Promise<void> {
    console.log(`[PPT模型切换] 切换到${floorId}楼层`);

    const { buildingData } = await import('@/data/buildingData');
    const floorData = buildingData.floors.find((floor) => floor.id === floorId);
    if (!floorData) {
      throw new Error(`找不到楼层数据: ${floorId}`);
    }

    await this.switchToModelPath(floorData.modelPath);
  }

  /**
   * 移除当前模型 - 轻量化清理（PPT专用）
   */
  private async removeCurrentModel(): Promise<void> {
    console.log('[PPT模型清理] 开始轻量化清理当前模型');

    // 1. 移除PPT模式下添加的模型
    if (this.currentModel && this.scene) {
      console.log('[PPT模型清理] 移除PPT当前模型');
      this.scene.remove(this.currentModel);
      this.currentModel = null;
    }

    // 2. 轻量化清理：只清理场景中的模型，不调用ModelLoaderManager的耗时清理
    if (this.scene) {
      const modelsToRemove: THREE.Object3D[] = [];

      this.scene.traverse((child) => {
        // 只清理PPT模式添加的模型，避免误删其他组件的模型
        if (child.userData.pptMode === true) {
          modelsToRemove.push(child);
        }
      });

      modelsToRemove.forEach((model) => {
        console.log(`[PPT模型清理] 清理残留模型: ${model.userData.type}`);
        this.scene!.remove(model);
      });

      if (modelsToRemove.length > 0) {
        console.log(`[PPT模型清理] 已清理 ${modelsToRemove.length} 个残留模型`);
      }
    }

    console.log('[PPT模型清理] 轻量化清理完成');
  }

  /**
   * 设置外景照明（简化版）
   */
  private setExteriorLighting(): void {
    const sceneManager = SceneManager.getInstance();
    if (sceneManager?.renderer) {
      sceneManager.renderer.setClearColor(0x87ceeb, 1); // 天蓝色背景
    }
  }

  /**
   * 设置内景照明（简化版）
   */
  private setInteriorLighting(): void {
    const sceneManager = SceneManager.getInstance();
    if (sceneManager?.renderer) {
      sceneManager.renderer.setClearColor(0x111111, 1); // 深色背景
    }
  }

  /**
   * 获取当前模型
   */
  getCurrentModel(): THREE.Object3D | null {
    return this.currentModel;
  }

  /**
   * 获取当前所有模型
   */
  getCurrentModels(): THREE.Object3D[] {
    return this.currentModel ? [this.currentModel] : [];
  }

  /**
   * 调试方法：检查缓存状态
   */
  async debugCacheStatus(): Promise<void> {
    if (!this.modelLoaderManager) {
      console.log('[PPT缓存调试] ModelLoaderManager未初始化');
      return;
    }

    const loadedModels = (this.modelLoaderManager as unknown as ModelLoaderManagerWithPrivates).loadedModels;
    console.log('[PPT缓存调试] 当前缓存的模型数量:', loadedModels.size);

    const { buildingData } = await import('@/data/buildingData');
    console.log('[PPT缓存调试] 外景模型缓存状态:', loadedModels.has(buildingData.modelPath) ? '✅已缓存' : '❌未缓存');

    buildingData.floors.forEach((floor: any) => {
      const cached = loadedModels.has(floor.modelPath);
      console.log(`[PPT缓存调试] ${floor.id}楼层模型缓存状态:`, cached ? '✅已缓存' : '❌未缓存');
    });
  }

  /**
   * 清理资源
   */
  async dispose(): Promise<void> {
    await this.removeCurrentModel();
    // 不需要dispose加载器，因为我们没有创建
  }
}

import { defHttp } from '/@/utils/http/axios';

// JeecgBoot标准API响应接口
export interface ApiResponse<T = any> {
  success: boolean;
  message: string;
  code: number;
  result: T;
  timestamp: number;
}

// PPT接口定义
export interface PPTItem {
  id: number;
  photoUrl?: string;
  dataJson?: string;
  orderNum?: number;
  name?: string;
  coverImage?: string;
  imageCount?: number;
  createTime?: string;
  images?: any[];
}

// PPT表单接口定义
export interface PPTForm {
  id?: number;
  name: string;
  images: any[];
}

// PPT列表响应接口
export interface PPTListResponse {
  records: PPTItem[];
  total: number;
  size: number;
  current: number;
  pages: number;
}

// PPT API 类
export class PPTApi {
  /**
   * 获取PPT列表
   */
  static async getPPTList(pageNo: number = 1, pageSize: number = 100): Promise<PPTListResponse> {
    return defHttp.get<PPTListResponse>({
      url: '/pptView',
      params: {
        pageNo,
        pageSize,
      },
    });
  }

  /**
   * 获取PPT详情
   */
  static async getPPTDetail(id: number): Promise<PPTItem> {
    return defHttp.get<PPTItem>({
      url: `/pptView/${id}`,
    });
  }

  /**
   * 创建PPT
   */
  static async createPPT(data: { name: string; photoUrl: string; dataJson: string; orderNum: number }): Promise<PPTItem> {
    return defHttp.post<PPTItem>({
      url: '/pptView',
      data,
    });
  }

  /**
   * 更新PPT
   */
  static async updatePPT(
    id: number,
    data: {
      name: string;
      photoUrl: string;
      dataJson: string;
      orderNum: number;
    }
  ): Promise<PPTItem> {
    return defHttp.put<PPTItem>({
      url: '/pptView',
      data: {
        id,
        ...data,
      },
    });
  }

  /**
   * 删除PPT
   */
  static async deletePPT(id: number): Promise<ApiResponse> {
    return defHttp.delete<ApiResponse>(
      {
        url: '/pptView',
        params: {
          idList: id.toString(), // 修正参数名为idList（大写L）
        },
      },
      { joinParamsToUrl: true }
    ); // 添加joinParamsToUrl选项
  }

  /**
   * 批量删除PPT
   */
  static async deletePPTBatch(ids: number[]): Promise<ApiResponse> {
    return defHttp.delete<ApiResponse>(
      {
        url: '/pptView',
        params: {
          idList: ids.join(','), // 修正参数名为idList（大写L）
        },
      },
      { joinParamsToUrl: true }
    ); // 添加joinParamsToUrl选项
  }

  /**
   * 上传文件
   */
  static async uploadFile(file: File): Promise<string> {
    const response = await defHttp.uploadFile(
      {
        url: '/sys/common/upload',
      },
      {
        file: file,
        name: 'file',
      },
      {
        isReturnResponse: true,
      }
    );

    if (response && response.success && response.message) {
      return response.message; // 返回相对路径
    } else {
      throw new Error('上传失败');
    }
  }
}

// 导出默认API实例
export default PPTApi;
